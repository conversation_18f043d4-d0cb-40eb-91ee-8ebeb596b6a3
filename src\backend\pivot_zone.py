"""
Pivot Zone Service - Backend calculation of pivot zones.

This module handles the calculation of pivot zones for the trading system.
All pivot zone calculations are performed in the backend to maintain separation of concerns
and ensure consistent, high-performance computations.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from .observability import get_structured_logger, trace_operation


@dataclass
class PivotZoneData:
    """Data structure for a pivot zone."""
    price: float
    zone_type: str  # 'pivot_high' or 'pivot_low'
    strength: int
    count: int
    winrate: float
    level_name: str


@dataclass
class PivotZoneCalculationResult:
    """Result of pivot zone calculations."""
    zones: List[PivotZoneData]
    pivot_high_zones: List[PivotZoneData]
    pivot_low_zones: List[PivotZoneData]
    calculation_metadata: Dict[str, Any]


class PivotZoneService:
    """
    Service for calculating pivot zones in the backend.
    
    This service handles all pivot zone-related calculations including:
    - Pivot point identification and classification
    - Strength calculations
    - Zone filtering and ranking
    """
    
    def __init__(self):
        self.logger = get_structured_logger("PivotZoneService")
        self._pivot_zone_cache: Dict[str, PivotZoneCalculationResult] = {}
        
    def calculate_pivot_zones_from_volatility_data(
        self,
        filtered_high_data: List[Any],
        filtered_low_data: List[Any],
        market_data: Optional[Dict[str, Any]] = None
    ) -> PivotZoneCalculationResult:
        """
        Calculate pivot zones by collecting min avg values from volatility graph
        on 1fwl HL matching and Weekday matching charts - ALL CALCULATIONS IN BACKEND.

        Args:
            filtered_high_data: High data for volatility calculations
            filtered_low_data: Low data for volatility calculations
            market_data: Market data for calculations

        Returns:
            PivotZoneCalculationResult containing all calculated pivot zones
        """
        with trace_operation("calculate_pivot_zones_from_volatility_data"):
            try:
                import time
                start_time = time.time()

                self.logger.info("Starting pivot zone calculations from volatility data")

                # Import volatility calculations service to get min avg values
                from .volatility_calculations_service import VolatilityCalculationsService
                volatility_service = VolatilityCalculationsService()

                # For HL matching, we need to apply HL matching filter first
                hl_filtered_high, hl_filtered_low = self._apply_hl_matching_filter(
                    filtered_high_data, filtered_low_data
                )

                hl_matching_data = volatility_service.calculate_volatility_data(
                    hl_filtered_high, hl_filtered_low, market_data
                )

                # For weekday matching, we need to apply weekday filter first
                weekday_filtered_high, weekday_filtered_low = self._apply_weekday_filter(
                    filtered_high_data, filtered_low_data
                )

                weekday_matching_data = volatility_service.calculate_volatility_data(
                    weekday_filtered_high, weekday_filtered_low, market_data
                )

                # Extract min avg values from both datasets
                pivot_zones = []

                # Collect min avg values from HL matching
                hl_highs_stats = hl_matching_data.get('highs_stats', {})
                hl_lows_stats = hl_matching_data.get('lows_stats', {})

                if hl_highs_stats.get('min_avg'):
                    pivot_zones.append(PivotZoneData(
                        price=hl_highs_stats['min_avg'],
                        zone_type='pivot_high',
                        strength=1,
                        count=hl_highs_stats.get('count', 0),
                        winrate=0.0,
                        level_name='HL_MinAvg_High'
                    ))

                if hl_lows_stats.get('min_avg'):
                    pivot_zones.append(PivotZoneData(
                        price=hl_lows_stats['min_avg'],
                        zone_type='pivot_low',
                        strength=1,
                        count=hl_lows_stats.get('count', 0),
                        winrate=0.0,
                        level_name='HL_MinAvg_Low'
                    ))

                # Collect min avg values from Weekday matching
                weekday_highs_stats = weekday_matching_data.get('highs_stats', {})
                weekday_lows_stats = weekday_matching_data.get('lows_stats', {})

                if weekday_highs_stats.get('min_avg'):
                    pivot_zones.append(PivotZoneData(
                        price=weekday_highs_stats['min_avg'],
                        zone_type='pivot_high',
                        strength=1,
                        count=weekday_highs_stats.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MinAvg_High'
                    ))

                if weekday_lows_stats.get('min_avg'):
                    pivot_zones.append(PivotZoneData(
                        price=weekday_lows_stats['min_avg'],
                        zone_type='pivot_low',
                        strength=1,
                        count=weekday_lows_stats.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MinAvg_Low'
                    ))

                # Separate into high and low zones
                pivot_high_zones = [zone for zone in pivot_zones if zone.zone_type == 'pivot_high']
                pivot_low_zones = [zone for zone in pivot_zones if zone.zone_type == 'pivot_low']

                # Calculate optimal zones (highest and lowest min avg values)
                optimal_zones = self._calculate_optimal_zones(pivot_zones)

                # Calculate total time taken
                end_time = time.time()
                calculation_time_seconds = round(end_time - start_time, 4)

                # Create calculation metadata
                metadata = {
                    "total_zones": len(pivot_zones),
                    "pivot_high_zones_count": len(pivot_high_zones),
                    "pivot_low_zones_count": len(pivot_low_zones),
                    "optimal_zones_count": len(optimal_zones),
                    "calculation_time_seconds": calculation_time_seconds,
                    "status": "min_avg_collection_complete",
                    "hl_matching_highs_min_avg": hl_highs_stats.get('min_avg', 0),
                    "hl_matching_lows_min_avg": hl_lows_stats.get('min_avg', 0),
                    "weekday_matching_highs_min_avg": weekday_highs_stats.get('min_avg', 0),
                    "weekday_matching_lows_min_avg": weekday_lows_stats.get('min_avg', 0),
                    "optimal_zones": optimal_zones
                }

                result = PivotZoneCalculationResult(
                    zones=pivot_zones,
                    pivot_high_zones=pivot_high_zones,
                    pivot_low_zones=pivot_low_zones,
                    calculation_metadata=metadata
                )

                self.logger.info(f"Pivot zone calculations completed: {len(pivot_zones)} total zones from min avg values")
                return result

            except Exception as e:
                self.logger.error(f"Error in pivot zone calculations: {e}")
                import traceback
                traceback.print_exc()
                # Return empty result on error
                return PivotZoneCalculationResult(
                    zones=[],
                    pivot_high_zones=[],
                    pivot_low_zones=[],
                    calculation_metadata={"error": str(e)}
                )

    def calculate_pivot_zones(
        self,
        market_data: Optional[Dict[str, Any]] = None,
        calculation_params: Optional[Dict[str, Any]] = None
    ) -> PivotZoneCalculationResult:
        """
        Calculate pivot zones from market data - ALL CALCULATIONS IN BACKEND.
        This is a placeholder method for future pivot zone implementations.

        Args:
            market_data: Market data for pivot calculations
            calculation_params: Optional parameters for pivot zone calculations

        Returns:
            PivotZoneCalculationResult containing all calculated pivot zones
        """
        with trace_operation("calculate_pivot_zones"):
            try:
                import time
                start_time = time.time()

                self.logger.info("Starting pivot zone calculations")

                # Placeholder for future pivot zone calculations
                # TODO: Implement actual pivot zone calculation logic

                # For now, return empty results
                pivot_high_zones = []
                pivot_low_zones = []
                all_zones = []

                # Calculate total time taken
                end_time = time.time()
                calculation_time_seconds = round(end_time - start_time, 4)

                # Create calculation metadata
                metadata = {
                    "total_zones": len(all_zones),
                    "pivot_high_zones_count": len(pivot_high_zones),
                    "pivot_low_zones_count": len(pivot_low_zones),
                    "calculation_time_seconds": calculation_time_seconds,
                    "status": "placeholder_implementation"
                }

                result = PivotZoneCalculationResult(
                    zones=all_zones,
                    pivot_high_zones=pivot_high_zones,
                    pivot_low_zones=pivot_low_zones,
                    calculation_metadata=metadata
                )

                self.logger.info(f"Pivot zone calculations completed: {len(all_zones)} total zones")
                return result

            except Exception as e:
                self.logger.error(f"Error in pivot zone calculations: {e}")
                # Return empty result on error
                return PivotZoneCalculationResult(
                    zones=[],
                    pivot_high_zones=[],
                    pivot_low_zones=[],
                    calculation_metadata={"error": str(e)}
                )
    
    def get_pivot_zones_for_display(
        self, 
        zones_result: PivotZoneCalculationResult,
        filter_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format pivot zones for display in the frontend.
        
        Args:
            zones_result: Result from pivot zone calculations
            filter_params: Optional filtering parameters
            
        Returns:
            Dictionary formatted for frontend display
        """
        try:
            # Apply any filtering if specified
            filtered_zones = self._apply_pivot_zone_filters(zones_result.zones, filter_params)
            
            # Format for display
            display_data = {
                "zones": [self._format_pivot_zone_for_display(zone) for zone in filtered_zones],
                "pivot_high_zones": [self._format_pivot_zone_for_display(zone) for zone in zones_result.pivot_high_zones],
                "pivot_low_zones": [self._format_pivot_zone_for_display(zone) for zone in zones_result.pivot_low_zones],
                "metadata": zones_result.calculation_metadata
            }
            
            return display_data
            
        except Exception as e:
            self.logger.error(f"Error formatting pivot zones for display: {e}")
            return {"zones": [], "pivot_high_zones": [], "pivot_low_zones": [], "metadata": {}}
    
    def _apply_pivot_zone_filters(
        self, 
        zones: List[PivotZoneData], 
        filter_params: Optional[Dict[str, Any]]
    ) -> List[PivotZoneData]:
        """Apply filtering to pivot zones based on parameters."""
        if not filter_params:
            return zones
            
        filtered = zones
        
        # TODO: Implement filtering logic based on requirements
        # Examples: minimum strength, minimum count, winrate thresholds, etc.
        
        return filtered
    
    def _format_pivot_zone_for_display(self, zone: PivotZoneData) -> Dict[str, Any]:
        """Format a single pivot zone for frontend display."""
        return {
            "price": zone.price,
            "zone_type": zone.zone_type,
            "strength": zone.strength,
            "count": zone.count,
            "winrate": zone.winrate,
            "level_name": zone.level_name
        }
    
    def _apply_hl_matching_filter(self, high_data: List[Any], low_data: List[Any]) -> Tuple[List[Any], List[Any]]:
        """
        Apply HL matching filter to high and low data.
        This replicates the HL matching filtering logic from volgraph_zones.
        """
        try:
            # Import the HL matching filtering logic from volgraph_zones
            from .volgraph_zones import VolgraphZonesService
            zones_service = VolgraphZonesService()

            # Use the existing HL matching filter from volgraph_zones
            filtered_high, filtered_low = zones_service._apply_hl_matching_filter(high_data, low_data)

            self.logger.debug(f"Applied HL matching filter: {len(filtered_high)} highs, {len(filtered_low)} lows")
            return filtered_high, filtered_low

        except Exception as e:
            self.logger.error(f"Error applying HL matching filter: {e}")
            # Return original data if filtering fails
            return high_data, low_data

    def _apply_weekday_filter(self, high_data: List[Any], low_data: List[Any]) -> Tuple[List[Any], List[Any]]:
        """
        Apply weekday matching filter to high and low data.
        This replicates the weekday filtering logic from volgraph_zones.
        """
        try:
            # Import the weekday filtering logic from volgraph_zones
            from .volgraph_zones import VolgraphZonesService
            zones_service = VolgraphZonesService()

            # Use the existing weekday matching filter from volgraph_zones
            filtered_high, filtered_low = zones_service._apply_weekday_matching_filter(high_data, low_data)

            self.logger.debug(f"Applied weekday filter: {len(filtered_high)} highs, {len(filtered_low)} lows")
            return filtered_high, filtered_low

        except Exception as e:
            self.logger.error(f"Error applying weekday filter: {e}")
            # Return original data if filtering fails
            return high_data, low_data

    def _calculate_optimal_zones(self, pivot_zones: List[PivotZoneData]) -> List[Dict[str, Any]]:
        """
        Calculate optimal zones by finding the highest and lowest min avg values
        from all collected pivot zone data.
        """
        try:
            if not pivot_zones:
                return []

            # Extract all prices from pivot zones
            all_prices = [zone.price for zone in pivot_zones if zone.price > 0]

            if not all_prices:
                return []

            # Find highest and lowest min avg values
            highest_price = max(all_prices)
            lowest_price = min(all_prices)

            optimal_zones = []

            # Find the zone(s) with the highest price
            highest_zones = [zone for zone in pivot_zones if zone.price == highest_price]
            for zone in highest_zones:
                optimal_zones.append({
                    "type": "highest_min_avg",
                    "price": zone.price,
                    "source_zone": zone.level_name,
                    "zone_type": zone.zone_type,
                    "count": zone.count,
                    "description": f"Highest Min Avg: {zone.level_name}"
                })

            # Find the zone(s) with the lowest price (only if different from highest)
            if lowest_price != highest_price:
                lowest_zones = [zone for zone in pivot_zones if zone.price == lowest_price]
                for zone in lowest_zones:
                    optimal_zones.append({
                        "type": "lowest_min_avg",
                        "price": zone.price,
                        "source_zone": zone.level_name,
                        "zone_type": zone.zone_type,
                        "count": zone.count,
                        "description": f"Lowest Min Avg: {zone.level_name}"
                    })

                # Calculate 25%, 50%, and 75% levels between highest and lowest
                price_range = highest_price - lowest_price

                # 25% level (25% up from lowest)
                level_25_price = lowest_price + (price_range * 0.25)
                optimal_zones.append({
                    "type": "range_25_percent",
                    "price": level_25_price,
                    "source_zone": "Calculated Range Level",
                    "zone_type": "range_level",
                    "count": 0,
                    "description": f"25% Level: {level_25_price:.2f} (25% between optimal levels)"
                })

                # 50% level (midpoint)
                level_50_price = lowest_price + (price_range * 0.50)
                optimal_zones.append({
                    "type": "range_50_percent",
                    "price": level_50_price,
                    "source_zone": "Calculated Range Level",
                    "zone_type": "range_level",
                    "count": 0,
                    "description": f"50% Level: {level_50_price:.2f} (midpoint between optimal levels)"
                })

                # 75% level (75% up from lowest)
                level_75_price = lowest_price + (price_range * 0.75)
                optimal_zones.append({
                    "type": "range_75_percent",
                    "price": level_75_price,
                    "source_zone": "Calculated Range Level",
                    "zone_type": "range_level",
                    "count": 0,
                    "description": f"75% Level: {level_75_price:.2f} (75% between optimal levels)"
                })

            self.logger.debug(f"Calculated {len(optimal_zones)} optimal zones from {len(pivot_zones)} pivot zones")
            return optimal_zones

        except Exception as e:
            self.logger.error(f"Error calculating optimal zones: {e}")
            return []

    def clear_cache(self):
        """Clear the pivot zone calculation cache."""
        self._pivot_zone_cache.clear()
        self.logger.debug("Pivot zone calculation cache cleared")
